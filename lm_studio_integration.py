#!/usr/bin/env python3
"""
LM Studio Embedding Integration for HopRAG
集成LM Studio的embedding API到HopRAG系统中
"""

import numpy as np
import requests
import json
from typing import List, Union
from sklearn.preprocessing import normalize
import time

class LMStudioEmbedding:
    """
    LM Studio Embedding API包装类
    模拟SentenceTransformer接口，使HopRAG可以无缝使用LM Studio的embedding服务
    """
    
    def __init__(self, 
                 base_url: str = "http://localhost:1234/v1", 
                 model_name: str = "text-embedding-nomic-embed-text-v1.5",
                 api_key: str = "lm-studio",
                 device: str = "auto"):
        """
        初始化LM Studio Embedding客户端
        
        Args:
            base_url: LM Studio API基础URL
            model_name: 模型名称
            api_key: API密钥（本地无需验证，可任意设置）
            device: 设备参数（保持兼容性，实际由LM Studio管理）
        """
        self.base_url = base_url.rstrip('/')
        self.model_name = model_name
        self.api_key = api_key
        self.device = device
        self.embedding_url = f"{self.base_url}/embeddings"
        
        # 测试连接并获取模型信息
        self._test_connection()
    
    def _test_connection(self):
        """测试与LM Studio的连接"""
        try:
            test_response = self._call_api(["test"])
            if test_response and len(test_response) > 0:
                self.embedding_dim = len(test_response[0])
                print(f"✅ LM Studio连接成功，embedding维度: {self.embedding_dim}")
            else:
                raise Exception("测试调用返回空结果")
        except Exception as e:
            print(f"❌ LM Studio连接失败: {e}")
            print(f"请确认：")
            print(f"1. LM Studio运行在 {self.base_url}")
            print(f"2. 模型 {self.model_name} 已加载")
            raise
    
    def _call_api(self, texts: List[str]) -> List[List[float]]:
        """
        调用LM Studio API获取embeddings
        
        Args:
            texts: 文本列表
            
        Returns:
            embeddings列表，每个embedding是float列表
        """
        payload = {
            "model": self.model_name,
            "input": texts
        }
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        try:
            response = requests.post(
                self.embedding_url, 
                json=payload, 
                headers=headers,
                timeout=30
            )
            response.raise_for_status()
            
            data = response.json()
            
            # 解析OpenAI格式响应
            embeddings = []
            for item in data["data"]:
                embeddings.append(item["embedding"])
            
            return embeddings
            
        except requests.exceptions.RequestException as e:
            print(f"API调用失败: {e}")
            raise
        except (KeyError, json.JSONDecodeError) as e:
            print(f"响应解析失败: {e}")
            print(f"原始响应: {response.text}")
            raise
    
    def encode(self, 
               sentences: Union[str, List[str]], 
               normalize_embeddings: bool = True,
               device: str = None,
               **kwargs) -> np.ndarray:
        """
        编码文本为embedding向量（兼容SentenceTransformer接口）
        
        Args:
            sentences: 单个文本或文本列表
            normalize_embeddings: 是否归一化向量
            device: 设备参数（保持兼容性）
            **kwargs: 其他参数（保持兼容性）
            
        Returns:
            numpy数组格式的embedding向量
        """
        # 统一处理输入格式
        if isinstance(sentences, str):
            sentences = [sentences]
            single_input = True
        else:
            single_input = False
        
        # 调用API获取embeddings
        embeddings = self._call_api(sentences)
        
        # 转换为numpy数组
        embeddings_array = np.array(embeddings, dtype=np.float32)
        
        # 归一化处理
        if normalize_embeddings:
            embeddings_array = normalize(embeddings_array, norm='l2', axis=1)
        
        # 返回格式处理
        if single_input:
            return embeddings_array[0]  # 返回单个向量
        else:
            return embeddings_array     # 返回向量矩阵


class LMStudioEmbeddingTester:
    """LM Studio Embedding测试工具"""
    
    @staticmethod
    def test_basic_functionality():
        """基础功能测试"""
        print("=== LM Studio Embedding 基础功能测试 ===")
        
        try:
            # 创建客户端
            client = LMStudioEmbedding()
            
            # 测试单个文本
            print("\n1. 测试单个文本编码...")
            single_text = "Hello, this is a test sentence."
            embedding = client.encode(single_text)
            print(f"单个文本embedding形状: {embedding.shape}")
            print(f"前5个值: {embedding[:5]}")
            
            # 测试批量文本
            print("\n2. 测试批量文本编码...")
            batch_texts = [
                "First test sentence.",
                "Second test sentence.",
                "Third test sentence."
            ]
            embeddings = client.encode(batch_texts)
            print(f"批量文本embedding形状: {embeddings.shape}")
            
            # 测试归一化
            print("\n3. 测试向量归一化...")
            norms = np.linalg.norm(embeddings, axis=1)
            print(f"向量L2范数: {norms}")
            
            print("\n✅ 所有测试通过！")
            return True
            
        except Exception as e:
            print(f"\n❌ 测试失败: {e}")
            return False
    
    @staticmethod
    def test_hoprag_integration():
        """测试与HopRAG的集成"""
        print("\n=== HopRAG集成测试 ===")
        
        try:
            from tool import get_doc_embeds
            
            # 创建LM Studio客户端
            lm_model = LMStudioEmbedding()
            
            # 模拟HopRAG调用
            test_docs = [
                "This is a sample document for testing.",
                "Another document with different content."
            ]
            
            # 使用get_doc_embeds函数
            embeddings = get_doc_embeds(test_docs, lm_model)
            print(f"HopRAG集成测试成功，embedding形状: {embeddings.shape}")
            
            return True
            
        except Exception as e:
            print(f"HopRAG集成测试失败: {e}")
            return False


if __name__ == "__main__":
    # 运行测试
    tester = LMStudioEmbeddingTester()
    
    # 基础功能测试
    if tester.test_basic_functionality():
        # HopRAG集成测试
        tester.test_hoprag_integration()