#!/usr/bin/env python3
"""
批量离线节点构建脚本
逐个处理文档，避免超时问题
"""
import os
import json
import pickle
import time
from HopBuilder import QABuilder
from config import node_name
from tqdm import tqdm

def batch_build_offline(docs_dir="quickstart_dataset/hotpot_example_docs", 
                       cache_dir='quickstart_dataset/cache_hotpot_offline',
                       max_docs=20):
    """批量构建离线节点"""
    print(f"开始批量离线构建，处理最多 {max_docs} 个文档...")
    
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
    
    # 加载已有缓存（如果存在）
    docid2nodes_path = f'{cache_dir}/docid2nodes.json'
    node2questiondict_path = f'{cache_dir}/node2questiondict.pkl'
    
    if os.path.exists(docid2nodes_path):
        with open(docid2nodes_path, 'r') as f:
            docid2nodes = json.load(f)
        print(f"加载已有 docid2nodes: {len(docid2nodes)} 个文档")
    else:
        docid2nodes = {}
    
    if os.path.exists(node2questiondict_path):
        with open(node2questiondict_path, 'rb') as f:
            node2questiondict = pickle.load(f)
        print(f"加载已有 node2questiondict: {len(node2questiondict)} 个节点")
    else:
        node2questiondict = {}
    
    # 获取所有文档
    docs_pool = os.listdir(docs_dir)
    docs_pool = [doc for doc in docs_pool if doc.endswith('.txt')]
    print(f"发现 {len(docs_pool)} 个文档")
    
    # 过滤已处理的文档
    remaining_docs = [doc for doc in docs_pool if doc not in docid2nodes]
    print(f"还需处理 {len(remaining_docs)} 个文档")
    
    if len(remaining_docs) == 0:
        print("所有文档已处理完成!")
        return True
    
    # 限制处理数量
    remaining_docs = remaining_docs[:max_docs]
    print(f"本次处理 {len(remaining_docs)} 个文档")
    
    # 初始化构建器
    builder = QABuilder(done=set(docid2nodes.keys()), label=node_name)
    
    # 当前最大节点ID
    current_max_id = 0
    if node2questiondict:
        current_max_id = max([node_id for node_id, _ in node2questiondict.keys()])
    
    # 逐个处理文档
    successful_docs = 0
    failed_docs = []
    
    for doc_id in tqdm(remaining_docs, desc="处理文档"):
        try:
            print(f"\n处理文档: {doc_id}")
            doc_path = os.path.join(docs_dir, doc_id)
            
            with open(doc_path, 'r', encoding='utf-8') as f:
                doc_content = f.read()
            
            print(f"  文档长度: {len(doc_content)} 字符")
            
            # 生成问答对
            sentence2node = builder.get_single_doc_qa(doc_content)
            
            if not sentence2node:
                print(f"  跳过文档 {doc_id}：无法生成节点")
                continue
            
            print(f"  生成了 {len(sentence2node)} 个节点")
            
            # 保存节点
            nodes_id = []
            for text, tup in sentence2node.items():
                current_max_id += 1
                node = {'text': tup[0], 'keywords': sorted(list(tup[1])), 'embed': tup[2]}
                node2questiondict[(current_max_id, doc_id)] = (node, tup[3])
                nodes_id.append(current_max_id)
            
            docid2nodes[doc_id] = nodes_id
            successful_docs += 1
            
            # 每处理一个文档就保存一次缓存
            print(f"  保存缓存...")
            with open(docid2nodes_path, 'w') as f:
                json.dump(docid2nodes, f)
            
            with open(node2questiondict_path, 'wb') as f:
                pickle.dump(node2questiondict, f)
            
            print(f"  成功处理文档 {doc_id}")
            
        except Exception as e:
            print(f"  错误处理文档 {doc_id}: {e}")
            failed_docs.append(doc_id)
            time.sleep(3)
            continue
    
    print(f"\n批量处理完成:")
    print(f"  成功处理: {successful_docs} 个文档")
    print(f"  失败处理: {len(failed_docs)} 个文档")
    print(f"  总计节点: {len(node2questiondict)} 个")
    print(f"  缓存目录: {cache_dir}")
    
    if failed_docs:
        print(f"  失败文档: {failed_docs}")
    
    return successful_docs > 0

if __name__ == "__main__":
    success = batch_build_offline(max_docs=10)
    if success:
        print("\n✅ 批量离线构建完成!")
    else:
        print("\n❌ 批量离线构建失败!")