#!/bin/bash

# 干净启动脚本 - 抑制所有不必要的终端输出

# 设置环境变量以抑制警告
export PYTHONWARNINGS="ignore::DeprecationWarning:requests:0,ignore::RequestsDependencyWarning:requests:0,ignore::DeprecationWarning:urllib3:0,ignore::DeprecationWarning:packaging:0,ignore::DeprecationWarning:pkg_resources:0,ignore::DeprecationWarning:setuptools:0,ignore::UserWarning,ignore::FutureWarning,ignore::PendingDeprecationWarning,ignore::ImportWarning"

# 抑制 conda 输出
export CONDA_AUTO_ACTIVATE_BASE=false
export CONDA_AUTO_UPDATE_CONDA=false

# 运行 Python 命令
exec python "$@"