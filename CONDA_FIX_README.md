# Conda 环境问题修复指南

## 问题描述

Conda 环境意外激活问题通常表现为：
- 每次启动终端时自动激活非 base 环境（如 tradingagents）
- 环境历史记录混乱
- 无法正常切换到期望的环境

## 解决方案

### 快速修复（推荐）

运行自动修复脚本：

```bash
# 1. 切换到项目目录
cd /Users/<USER>/Code/HopRAG

# 2. 运行修复脚本
./conda_fix.sh fix
```

此脚本将自动执行：
- ✅ 切换回 base 环境
- ✅ 清理环境历史记录
- ✅ 设置预防措施
- ✅ 验证修复效果

### 手动修复步骤

如果需要手动执行：

```bash
# 1. 切换到 base 环境
conda deactivate
conda activate base

# 2. 清理环境历史记录
cp ~/.conda/environments.txt ~/.conda/environments.txt.backup
conda env list | grep -E '^[^#]' | awk '{print $1}' > ~/.conda/environments.txt

# 3. 设置预防配置
conda config --set auto_activate_base true
export CONDA_AUTO_ACTIVATE_BASE=true
export CONDA_DEFAULT_ENV=base
```

## 预防措施

### 1. 项目级预防

已创建的预防文件：
- `.envrc` - direnv 环境配置
- `conda_fix.sh` - 修复脚本
- `conda_env_manager.py` - Python 管理工具

### 2. Shell 配置预防

在 `~/.zshrc` 或 `~/.bashrc` 中添加：

```bash
# Conda 环境管理配置
export CONDA_AUTO_ACTIVATE_BASE=true
export CONDA_DEFAULT_ENV=base
```

### 3. 定期监控

```bash
# 每月执行一次环境检查
./conda_fix.sh monitor

# 或使用 Python 工具
python conda_env_manager.py
```

## 工具说明

### conda_fix.sh

功能全面的修复脚本：

```bash
./conda_fix.sh fix      # 完整修复
./conda_fix.sh monitor  # 状态监控
./conda_fix.sh prevent  # 设置预防
./conda_fix.sh clean    # 清理历史
./conda_fix.sh help     # 帮助信息
```

### conda_env_manager.py

Python 环境管理工具：
- 自动检测问题
- 批量修复功能
- 详细日志记录
- 环境状态监控

### 环境变量说明

| 变量 | 推荐值 | 说明 |
|------|--------|------|
| CONDA_AUTO_ACTIVATE_BASE | true | 自动激活 base 环境 |
| CONDA_DEFAULT_ENV | base | 默认环境设置为 base |
| CONDA_AUTO_UPDATE_CONDA | false | 禁用自动更新 |

## 故障排除

### 常见问题

**Q: 修复后仍然激活错误环境？**
A: 检查 shell 配置文件，确保没有冲突的 conda 配置

**Q: direnv 提示文件被阻止？**
A: 运行 `direnv allow` 批准 .envrc 文件

**Q: conda 命令找不到？**
A: 检查 conda 安装路径：`which conda`

### 调试命令

```bash
# 查看环境状态
conda info --envs
echo $CONDA_DEFAULT_ENV

# 检查配置
conda config --show
env | grep CONDA

# 查看环境历史
cat ~/.conda/environments.txt
```

## 项目特定配置

对于 HopRAG 项目，建议的环境配置：

```python
# config.py 中的相关配置
llm_device = get_optimal_device()
embed_model = 'bge_en'  # 或 'lm_studio'
max_thread_num = 1      # GLM 模型设为 1
```

## 最佳实践

1. **定期维护**：每月运行一次环境检查
2. **备份重要配置**：修复前备份 `~/.conda/environments.txt`
3. **统一环境**：团队项目使用相同的基础环境配置
4. **文档记录**：记录环境问题的解决方案
5. **自动化**：将环境检查加入 CI/CD 流程

## 联系和支持

如遇问题，请：
1. 查看日志文件：`conda_env_manager.log`
2. 使用监控命令：`./conda_fix.sh monitor`
3. 检查常见问题部分
4. 如有需要，重新运行修复脚本

---

**最后更新**: 2025-01-21
**适用项目**: HopRAG 图检索增强生成系统