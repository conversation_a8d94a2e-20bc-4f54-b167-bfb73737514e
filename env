#!/bin/bash

# HopRAG 环境管理便捷脚本
# 自动选择可用的环境管理器（Python 优先）

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 尝试使用 Python 版本
if command -v python3 >/dev/null 2>&1 && [ -x "$SCRIPT_DIR/env_manager.py" ]; then
    exec python3 "$SCRIPT_DIR/env_manager.py" "$@"
# 回退到 shell 版本
elif [ -x "$SCRIPT_DIR/env-manager.sh" ]; then
    exec "$SCRIPT_DIR/env-manager.sh" "$@"
else
    echo "错误: 找不到可用的环境管理器脚本"
    echo "请确保 env_manager.py 或 env-manager.sh 存在且可执行"
    exit 1
fi