#!/usr/bin/env python3
"""
Conda 环境管理工具 - 防止环境激活问题
用于管理和监控 conda 环境，确保不会出现意外的默认环境激活
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from typing import List, Dict, Set
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('conda_env_manager.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class CondaEnvironmentManager:
    def __init__(self):
        self.conda_path = self._find_conda_path()
        self.environments_txt = Path.home() / ".conda" / "environments.txt"

    def _find_conda_path(self) -> str:
        """查找 conda 安装路径"""
        try:
            result = subprocess.run(
                ["which", "conda"],
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError:
            # 尝试常见路径
            common_paths = [
                "/opt/anaconda3/bin/conda",
                "/usr/local/anaconda3/bin/conda",
                "/Applications/anaconda3/bin/conda",
                "~/anaconda3/bin/conda",
                "~/miniconda3/bin/conda"
            ]

            for path in common_paths:
                expanded_path = os.path.expanduser(path)
                if os.path.exists(expanded_path):
                    return expanded_path

            raise RuntimeError("找不到 conda 安装路径")

    def get_current_env(self) -> str:
        """获取当前激活的环境"""
        return os.environ.get('CONDA_DEFAULT_ENV', 'base')

    def list_environments(self) -> List[str]:
        """列出所有可用环境"""
        try:
            result = subprocess.run(
                [self.conda_path, "env", "list", "--json"],
                capture_output=True,
                text=True,
                check=True
            )
            env_data = json.loads(result.stdout)
            return [env['name'] for env in env_data['envs']]
        except Exception as e:
            logging.error(f"获取环境列表失败: {e}")
            return []

    def set_base_as_default(self):
        """设置 base 为默认环境"""
        try:
            # 更新 conda 配置
            subprocess.run([
                self.conda_path, "config",
                "--set", "auto_activate_base", "true"
            ], check=True)

            # 设置环境变量
            os.environ['CONDA_AUTO_ACTIVATE_BASE'] = 'true'
            os.environ['CONDA_DEFAULT_ENV'] = 'base'

            logging.info("已设置 base 为默认环境")
        except Exception as e:
            logging.error(f"设置默认环境失败: {e}")

    def clean_environment_history(self):
        """清理环境历史记录"""
        try:
            if self.environments_txt.exists():
                # 备份原文件
                backup_path = self.environments_txt.with_suffix('.txt.bak')
                import shutil
                shutil.copy2(self.environments_txt, backup_path)

                # 重新创建干净的环境文件
                environments = self.list_environments()
                with open(self.environments_txt, 'w') as f:
                    for env in environments:
                        f.write(f"{env}\n")

                logging.info("环境历史记录已清理")
            else:
                logging.warning("environments.txt 文件不存在")
        except Exception as e:
            logging.error(f"清理环境历史记录失败: {e}")

    def create_env_guard(self):
        """创建环境保护机制"""
        guard_script = f"""
# Conda 环境守护脚本
# 此脚本确保始终使用正确的环境

import os
import sys

def check_environment():
    current_env = os.environ.get('CONDA_DEFAULT_ENV', 'base')

    # 如果当前环境不是 base 或期望的环境，切换到 base
    if current_env not in ['base', os.environ.get('EXPECTED_ENV', 'base')]:
        print(f"警告: 当前环境是 {{current_env}}，正在切换到 base")
        os.system("conda activate base")

    # 设置环境变量防止意外激活
    os.environ['CONDA_AUTO_ACTIVATE_BASE'] = 'true'
    os.environ['CONDA_DEFAULT_ENV'] = 'base'

if __name__ == "__main__":
    check_environment()
"""

        guard_path = Path("conda_env_guard.py")
        with open(guard_path, 'w') as f:
            f.write(guard_script)

        # 使其可执行
        os.chmod(guard_path, 0o755)

        logging.info("环境守护脚本已创建")

    def monitor_environments(self):
        """监控环境状态"""
        print("=== Conda 环境状态监控 ===")
        print(f"当前环境: {self.get_current_env()}")
        print(f"Conda 路径: {self.conda_path}")
        print(f"环境文件: {self.environments_txt}")

        print("\n可用环境:")
        environments = self.list_environments()
        for env in environments:
            marker = " (当前)" if env == self.get_current_env() else ""
            print(f"  - {env}{marker}")

        print(f"\n环境变量:")
        conda_vars = {k: v for k, v in os.environ.items()
                     if k.startswith('CONDA_')}
        for k, v in conda_vars.items():
            print(f"  {k} = {v}")

    def fix_common_issues(self):
        """修复常见问题"""
        issues_fixed = []

        # 问题1: 检查 CONDA_AUTO_ACTIVATE_BASE 设置
        auto_activate = os.environ.get('CONDA_AUTO_ACTIVATE_BASE', 'false')
        if auto_activate.lower() != 'true':
            self.set_base_as_default()
            issues_fixed.append("设置 CONDA_AUTO_ACTIVATE_BASE=true")

        # 问题2: 清理环境历史
        self.clean_environment_history()
        issues_fixed.append("清理环境历史记录")

        # 问题3: 创建守护脚本
        self.create_env_guard()
        issues_fixed.append("创建环境守护脚本")

        # 问题4: 验证 conda 配置
        try:
            subprocess.run([
                self.conda_path, "config", "--show"
            ], check=True, capture_output=True)
            issues_fixed.append("验证 conda 配置")
        except Exception as e:
            logging.error(f"配置验证失败: {e}")

        return issues_fixed

def main():
    """主函数"""
    manager = CondaEnvironmentManager()

    print("Conda 环境管理工具")
    print("=" * 50)

    # 显示当前状态
    manager.monitor_environments()

    print("\n" + "=" * 50)
    print("正在修复常见问题...")

    # 修复问题
    fixed_issues = manager.fix_common_issues()

    print("\n修复完成的问题:")
    for issue in fixed_issues:
        print(f"  ✓ {issue}")

    print("\n建议的预防措施:")
    print("1. 在项目根目录运行: python conda_env_manager.py")
    print("2. 添加到 shell 配置文件: source conda_env_guard.py")
    print("3. 定期检查环境状态: python conda_env_manager.py --monitor")

    print(f"\n日志文件位置: conda_env_manager.log")

if __name__ == "__main__":
    main()