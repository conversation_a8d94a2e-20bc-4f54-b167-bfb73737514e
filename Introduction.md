# HopRAG: Graph-Enhanced Retrieval-Augmented Generation

## 概述

HopRAG 是一个基于知识图谱的检索增强生成(RAG)系统，通过将文档结构化为问答驱动的图来实现多跳推理能力。系统将文档段落存储在Neo4j图数据库中，利用图结构进行多步骤推理，从而为复杂问题提供更精确的答案。

## 核心架构

### 1. 系统组件概览

```
文档集合 → HopBuilder → 知识图谱 → HopRetriever → HopGenerator → 答案
         (图构建)     (Neo4j)    (图检索)      (答案生成)
```

### 2. 主要模块

#### A. HopBuilder.py - 图构建器
**核心类**: `QABuilder`
- **职责**: 将原始文档转换为问答驱动的知识图谱
- **关键方法**:
  - `get_single_doc_qa()`: 从单个文档生成问答对
  - `create_nodes()`: 在线创建图节点
  - `create_nodes_offline()`: 离线创建节点
  - `create_edge()`: 创建节点间的连接边

#### B. HopRetriever.py - 图检索器
**核心类**: `HopRetriever`
- **职责**: 在知识图谱中执行多跳检索
- **关键方法**:
  - `hybrid_retrieve_node/edge()`: 混合检索(稠密+稀疏)
  - `find_entry_node()`: 查找检索起始节点
  - `get_llm_choice()`: 使用LLM决定遍历路径
  - `search_docs()`: 主要检索入口点

#### C. HopGenerator.py - RAG管道
**核心类**: `RagPipeline`
- **职责**: 整合检索和生成流程
- **关键方法**:
  - `retrieve()`: 标准检索
  - `reformulate_retrieve()`: 查询重构检索
  - `rag()`: 端到端RAG处理

## 算法详解

### 1. 图构建算法

#### 1.1 文档分块与节点创建
```python
# 伪代码流程
for document in document_collection:
    chunks = document.split(signal)  # 按分隔符分块
    for chunk in chunks:
        # 为每个chunk生成两类问题
        answerable_questions = generate_questions(chunk, "answerable_template")
        pending_questions = generate_questions(chunk, "pending_template")
        
        # 创建节点
        node = {
            "text": chunk,
            "keywords": extract_keywords(chunk),
            "embedding": get_embedding(chunk),
            "questions": {
                "answerable": answerable_questions,
                "pending": pending_questions
            }
        }
        graph.add_node(node)
```

#### 1.2 边创建算法
**核心思想**: 通过问题相似性连接节点
- **Pending问题**: 需要额外信息才能回答的问题
- **Answerable问题**: 基于当前文本可直接回答的问题
- **边的创建**: Pending问题连接到相关的Answerable问题

```python
# 边创建流程
for pending_question in all_pending_questions:
    best_match = find_most_similar_answerable_question(pending_question)
    if similarity_score > threshold:
        create_edge(pending_question.node, best_match.node, 
                   weight=similarity_score)
```

### 2. 检索算法

#### 2.1 混合检索策略
```python
def hybrid_retrieve(query):
    # 1. 处理查询
    query_embedding = get_embedding(query)
    query_keywords = extract_keywords(query)
    
    # 2. 稠密检索(语义相似性)
    dense_results = vector_search(query_embedding)
    
    # 3. 稀疏检索(关键词匹配)
    sparse_results = keyword_search(query_keywords)
    
    # 4. 混合评分
    hybrid_results = combine_scores(dense_results, sparse_results)
    
    return hybrid_results
```

#### 2.2 多跳遍历算法
支持多种遍历策略:
- **BFS (广度优先)**: 逐层扩展，适合发现相关性强的邻近节点
- **DFS (深度优先)**: 深入特定路径，适合需要深度推理的查询
- **BFS_SIM_NODE**: 基于节点相似性的BFS变体

```python
def multi_hop_search(entry_nodes, max_hop=5):
    visited = set()
    current_layer = entry_nodes
    context = []
    
    for hop in range(max_hop):
        next_layer = []
        for node in current_layer:
            if node not in visited:
                context.append(node.text)
                visited.add(node)
                
                # 使用LLM决定是否继续遍历
                if should_continue_traversal(node, query):
                    neighbors = get_neighbors(node)
                    next_layer.extend(neighbors)
        
        current_layer = next_layer
        if not current_layer:
            break
    
    return context
```

### 3. 生成算法

#### 3.1 标准RAG流程
```python
def rag_pipeline(query):
    # 1. 检索相关上下文
    context = multi_hop_search(query)
    
    # 2. 构建提示模板
    prompt = f"""
    Question: {query}
    Context: {context}
    Please answer based on the given context.
    """
    
    # 3. 生成答案
    answer = llm_generate(prompt)
    
    return answer, context
```

## 数据流与执行逻辑

### 1. 构建阶段数据流
```mermaid
graph TD
    A[原始数据集] --> B[数据预处理]
    B --> C[文档池]
    C --> D[节点构建]
    D --> E[问题生成]
    E --> F[嵌入计算]
    F --> G[边构建]
    G --> H[相似性计算]
    H --> I[图索引创建]
    I --> J[Neo4j存储]
```

**详细流程**:
1. **数据预处理**: 将JSON/JSONL格式的数据集转换为文档池
2. **节点构建**: 
   - 文档分块(按`\n\n`分割)
   - 为每个块生成关键词和嵌入
   - 生成两类问题(answerable/pending)
3. **边构建**:
   - 计算所有pending问题与answerable问题的相似性
   - 创建相似性超过阈值的连接
   - 优化边的数量和质量

### 2. 检索阶段执行流程
```mermaid
graph TD
    A[用户查询] --> B[查询预处理]
    B --> C[混合检索]
    C --> D[入口节点发现]
    D --> E[多跳图遍历]
    E --> F{是否继续?}
    F -->|是| G[LLM路径选择]
    G --> H[扩展邻居节点]
    H --> E
    F -->|否| I[上下文收集]
    I --> J[答案生成]
```

**关键步骤**:
1. **查询预处理**: 生成嵌入向量和提取关键词
2. **入口节点发现**: 通过节点检索或边检索找到起始点
3. **多跳遍历**: 
   - 基于相似性和LLM判断选择遍历路径
   - 收集每一跳的相关节点文本
   - 避免重复访问(visited set)
4. **上下文整合**: 将多跳收集的文本组织为连贯上下文

## 算法要点与创新

### 1. 问答驱动的图构建
**创新点**: 不同于传统的实体-关系图，HopRAG使用问题作为连接机制
- **优势**: 更自然地反映信息需求和推理路径
- **机制**: Pending问题表示信息缺口，Answerable问题提供信息补充

### 2. 混合检索策略
**技术特点**:
- **稠密检索**: 基于语义嵌入的相似性匹配
- **稀疏检索**: 基于关键词的精确匹配
- **动态权重**: 根据查询类型自适应调整权重比例

### 3. LLM引导的图遍历
**核心机制**:
- 在每一跳使用LLM判断是否继续遍历
- 根据当前上下文和目标问题选择最优路径
- 避免无关路径的盲目探索

### 4. 多数据集适配
**支持的数据集**:
- **HotpotQA**: 多跳问答基准
- **2WikiMultiHop**: 维基百科多跳推理
- **MuSiQue**: 多步骤推理问答

## 配置与部署

### 1. 核心配置参数
```python
# 数据库配置
neo4j_url = "bolt://localhost:7687"
neo4j_user = "neo4j"
neo4j_password = "password"

# 模型配置
embed_model = "sentence-transformers/all-MiniLM-L6-v2"
query_generator_model = "gpt-3.5-turbo"
traversal_model = "gpt-4o-mini"

# 图结构配置
node_name = "hotpot_example"
edge_name = "pending2answerable"
max_hop = 5
```

### 2. 关键超参数
- **max_hop**: 最大跳数(默认5)
- **topk**: 检索返回的文档数量(默认8-10)
- **signal**: 文档分块分隔符(默认"\n\n")
- **max_thread_num**: 并行处理线程数

## 性能特点

### 1. 优势
- **多跳推理能力**: 能够处理需要多步推理的复杂问题
- **图结构优势**: 保持文档间的语义关联
- **混合检索**: 结合语义和关键词检索的优势
- **可扩展性**: 支持大规模文档集合

### 2. 局限性
- **构建成本**: 图构建需要大量计算资源
- **存储需求**: Neo4j图数据库需要额外存储空间
- **复杂性**: 系统配置和调优相对复杂

## 使用示例

### 1. 快速开始
```bash
# 1. 构建图(离线模式)
python HopBuilder.py

# 2. 运行查询
python HopGenerator.py --data_path quickstart_dataset/hotpot_example.jsonl \
                       --save_dir quickstart_dataset/hotpot_output \
                       --max_hop 5 \
                       --entry_type node \
                       --topk 8
```

### 2. 典型查询流程
```python
# 初始化系统
args = parse_args()
pipeline = RagPipeline(args)

# 执行查询
query = "What league does the team that Donnie Smith plays for belong to?"
answer, context = pipeline.rag(query)
print(f"Answer: {answer}")
print(f"Context: {context}")
```

## 总结

HopRAG 代表了检索增强生成技术的重要进展，通过将文档知识结构化为问答驱动的图，实现了更精确和可控的多跳推理能力。该系统特别适合处理需要跨文档信息整合的复杂问答任务，为知识密集型应用提供了强有力的技术支撑。