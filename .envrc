# Suppress all Python warnings including dependency warnings
export PYTHONWARNINGS="ignore::DeprecationWarning:requests:0,ignore::RequestsDependencyWarning:requests:0,ignore::DeprecationWarning:urllib3:0,ignore::DeprecationWarning:packaging:0,ignore::DeprecationWarning:pkg_resources:0,ignore::DeprecationWarning:setuptools:0,ignore::UserWarning,ignore::FutureWarning,ignore::PendingDeprecationWarning,ignore::ImportWarning"

# Silence conda info output
export CONDA_AUTO_ACTIVATE_BASE=false
export CONDA_AUTO_UPDATE_CONDA=false

# Clean direnv output
export DIRENV_LOG_FORMAT=""
