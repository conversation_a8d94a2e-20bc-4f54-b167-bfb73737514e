2025-08-13 18:00:04.132 | INFO     | chat_terminal:__init__:87 - 初始化RAG管道...
2025-08-13 18:00:04.132 | ERROR    | __main__:test_basic_functionality:60 - 测试出错
Traceback (most recent call last):

  File "/Users/<USER>/Code/HopRAG/test_chat.py", line 116, in <module>
    main()
    └ <function main at 0x3125a2520>

  File "/Users/<USER>/Code/HopRAG/test_chat.py", line 96, in main
    basic_test = test_basic_functionality()
                 └ <function test_basic_functionality at 0x10133d080>

> File "/Users/<USER>/Code/HopRAG/test_chat.py", line 17, in test_basic_functionality
    chat_system = TerminalChat(enable_akshare=True, history_file="test_history.json")
                  └ <class 'chat_terminal.TerminalChat'>

  File "/Users/<USER>/Code/HopRAG/chat_terminal.py", line 88, in __init__
    self.rag_pipeline = RagPipeline(enable_akshare=enable_akshare)
    │                   │                          └ True
    │                   └ <class 'HopGenerator.RagPipeline'>
    └ <chat_terminal.TerminalChat object at 0x101326300>

TypeError: RagPipeline.__init__() got an unexpected keyword argument 'enable_akshare'
