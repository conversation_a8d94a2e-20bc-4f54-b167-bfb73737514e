2025-08-13 19:05:15.767 | INFO     | chat_terminal:__init__:87 - 初始化RAG管道...
2025-08-13 19:05:22.899 | INFO     | HopGenerator:_init_akshare_adapter:111 - AKShare适配器组件初始化完成
2025-08-13 19:05:22.899 | INFO     | HopGenerator:__init__:80 - AKShare适配器初始化成功
2025-08-13 19:05:22.899 | INFO     | chat_terminal:__init__:124 - 聊天系统初始化完成
2025-08-13 19:05:25.155 | ERROR    | HopGenerator:retrieve:246 - 文档检索失败: {code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.vector.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such vector schema index: hotpot_bgeen_qwen1b5_node_dense_index}
2025-08-13 19:05:25.179 | ERROR    | HopGenerator:rag:353 - RAG流程执行失败: {code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.vector.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such vector schema index: hotpot_bgeen_qwen1b5_node_dense_index}
2025-08-13 19:05:25.202 | ERROR    | HopGenerator:retrieve:246 - 文档检索失败: {code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.vector.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such vector schema index: hotpot_bgeen_qwen1b5_node_dense_index}
2025-08-13 19:05:25.226 | ERROR    | HopGenerator:rag:364 - 降级处理也失败: {code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.vector.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such vector schema index: hotpot_bgeen_qwen1b5_node_dense_index}
2025-08-13 19:05:25.491 | ERROR    | HopGenerator:retrieve:246 - 文档检索失败: {code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.vector.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such vector schema index: hotpot_bgeen_qwen1b5_node_dense_index}
2025-08-13 19:05:25.519 | ERROR    | HopGenerator:rag:353 - RAG流程执行失败: {code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.vector.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such vector schema index: hotpot_bgeen_qwen1b5_node_dense_index}
2025-08-13 19:05:25.547 | ERROR    | HopGenerator:retrieve:246 - 文档检索失败: {code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.vector.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such vector schema index: hotpot_bgeen_qwen1b5_node_dense_index}
2025-08-13 19:05:25.573 | ERROR    | HopGenerator:rag:364 - 降级处理也失败: {code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.vector.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such vector schema index: hotpot_bgeen_qwen1b5_node_dense_index}
2025-08-13 19:05:25.765 | ERROR    | HopGenerator:retrieve:246 - 文档检索失败: {code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.vector.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such vector schema index: hotpot_bgeen_qwen1b5_node_dense_index}
2025-08-13 19:05:25.939 | ERROR    | HopGenerator:rag:353 - RAG流程执行失败: {code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.vector.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such vector schema index: hotpot_bgeen_qwen1b5_node_dense_index}
2025-08-13 19:05:25.969 | ERROR    | HopGenerator:retrieve:246 - 文档检索失败: {code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.vector.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such vector schema index: hotpot_bgeen_qwen1b5_node_dense_index}
2025-08-13 19:05:25.990 | ERROR    | HopGenerator:rag:364 - 降级处理也失败: {code: Neo.ClientError.Procedure.ProcedureCallFailed} {message: Failed to invoke procedure `db.index.vector.queryNodes`: Caused by: java.lang.IllegalArgumentException: There is no such vector schema index: hotpot_bgeen_qwen1b5_node_dense_index}
