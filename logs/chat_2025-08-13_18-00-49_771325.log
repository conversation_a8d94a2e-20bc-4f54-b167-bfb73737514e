2025-08-13 18:00:49.774 | INFO     | chat_terminal:__init__:87 - 初始化RAG管道...
2025-08-13 18:00:49.774 | ERROR    | __main__:test_basic_functionality:60 - 测试出错
Traceback (most recent call last):

  File "/Users/<USER>/Code/HopRAG/test_chat.py", line 139, in <module>
    main()
    └ <function main at 0x3227a2480>

  File "/Users/<USER>/Code/HopRAG/test_chat.py", line 119, in main
    basic_test = test_basic_functionality()
                 └ <function test_basic_functionality at 0x10116a340>

> File "/Users/<USER>/Code/HopRAG/test_chat.py", line 17, in test_basic_functionality
    chat_system = TerminalChat(enable_akshare=True, history_file="test_history.json")
                  └ <class 'chat_terminal.TerminalChat'>

  File "/Users/<USER>/Code/HopRAG/chat_terminal.py", line 112, in __init__
    self.rag_pipeline = RagPipeline(args)
    │                   │           └ Namespace(enable_akshare=True, model_name='glm-4.5', traversal_model='glm-4.5', embedding_model='bge_en', rerank_model=None, ...
    │                   └ <class 'HopGenerator.RagPipeline'>
    └ <chat_terminal.TerminalChat object at 0x101242f90>

  File "/Users/<USER>/Code/HopRAG/HopGenerator.py", line 68, in __init__
    self.retriever = self._get_retriever()
    │                │    └ <function RagPipeline._get_retriever at 0x3227a1120>
    │                └ <HopGenerator.RagPipeline object at 0x320510a10>
    └ <HopGenerator.RagPipeline object at 0x320510a10>

  File "/Users/<USER>/Code/HopRAG/HopGenerator.py", line 88, in _get_retriever
    if self.args.retriever_name == "HopRetriever":
       │    └ Namespace(enable_akshare=True, model_name='glm-4.5', traversal_model='glm-4.5', embedding_model='bge_en', rerank_model=None, ...
       └ <HopGenerator.RagPipeline object at 0x320510a10>

AttributeError: 'Namespace' object has no attribute 'retriever_name'. Did you mean: 'retrieve_only'?
