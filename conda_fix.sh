#!/bin/bash

# Conda 环境修复和预防脚本
# 用法: ./conda_fix.sh [command]
#
# Commands:
#   fix         - 修复当前环境问题
#   monitor     - 监控环境状态
#   prevent     - 设置预防措施
#   clean       - 清理环境历史
#   help        - 显示帮助信息

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 conda 是否可用
check_conda() {
    if ! command -v conda &> /dev/null; then
        log_error "Conda 未找到，请确保 conda 已正确安装"
        exit 1
    fi
}

# 切换到 base 环境
switch_to_base() {
    log_info "切换到 base 环境..."
    conda activate base 2>/dev/null || true

    # 验证当前环境
    current_env=$(conda info --envs | grep '*' | awk '{print $1}')
    if [ "$current_env" != "base" ]; then
        log_warn "当前环境: $current_env，尝试强制切换..."
        export CONDA_DEFAULT_ENV=base
        source activate base 2>/dev/null || true
    fi

    log_info "当前环境: $(conda info --envs | grep '*' | awk '{print $1}')"
}

# 清理环境历史记录
clean_env_history() {
    log_info "清理 conda 环境历史记录..."

    # 备份原文件
    if [ -f ~/.conda/environments.txt ]; then
        cp ~/.conda/environments.txt ~/.conda/environments.txt.backup
        log_info "已备份原环境文件到 ~/.conda/environments.txt.backup"
    fi

    # 重新生成环境列表
    conda env list | grep -E '^[^#]' | awk '{print $1}' > ~/.conda/environments.txt
    log_info "环境历史记录已更新"
}

# 设置预防措施
setup_prevention() {
    log_info "设置环境预防措施..."

    # 1. 配置 conda 自动激活 base
    conda config --set auto_activate_base true
    log_info "已设置 auto_activate_base = true"

    # 2. 更新环境变量
    export CONDA_AUTO_ACTIVATE_BASE=true
    export CONDA_DEFAULT_ENV=base

    # 3. 更新 shell 配置文件
    SHELL_RC=""
    if [ -n "$ZSH_VERSION" ]; then
        SHELL_RC="$HOME/.zshrc"
    elif [ -n "$BASH_VERSION" ]; then
        SHELL_RC="$HOME/.bashrc"
    else
        log_warn "无法确定 shell 类型，使用 .bashrc"
        SHELL_RC="$HOME/.bashrc"
    fi

    # 检查是否已存在相关配置
    if ! grep -q "CONDA_AUTO_ACTIVATE_BASE" "$SHELL_RC"; then
        echo "# Conda 环境管理配置" >> "$SHELL_RC"
        echo "export CONDA_AUTO_ACTIVATE_BASE=true" >> "$SHELL_RC"
        echo "export CONDA_DEFAULT_ENV=base" >> "$SHELL_RC"
        log_info "已更新 $SHELL_RC"
    else
        log_info "$SHELL_RC 已包含相关配置"
    fi

    # 4. 创建环境监控脚本
    cat > conda_monitor.sh << 'EOF'
#!/bin/bash
# Conda 环境监控脚本
# 定期检查环境状态，防止意外环境激活

CURRENT_ENV=${CONDA_DEFAULT_ENV:-base}
EXPECTED_ENV=${EXPECTED_ENV:-base}

if [ "$CURRENT_ENV" != "$EXPECTED_ENV" ]; then
    echo "警告: 当前环境是 $CURRENT_ENV，期望环境是 $EXPECTED_ENV"
    echo "建议运行: conda activate $EXPECTED_ENV"
fi
EOF

    chmod +x conda_monitor.sh
    log_info "已创建环境监控脚本: conda_monitor.sh"
}

# 监控环境状态
monitor_env() {
    echo "=== Conda 环境状态监控 ==="
    echo "当前时间: $(date)"
    echo "当前环境: ${CONDA_DEFAULT_ENV:-base}"
    echo "Conda 版本: $(conda --version)"
    echo ""

    echo "环境列表:"
    conda env list
    echo ""

    echo "环境变量:"
    env | grep -E '^CONDA_' | sort
    echo ""

    echo "配置文件状态:"
    if [ -f ~/.conda/environments.txt ]; then
        echo "environments.txt 存在，包含环境:"
        cat ~/.conda/environments.txt | nl
    else
        echo "environments.txt 不存在"
    fi
    echo ""

    # 检查常见问题
    echo "问题检查:"
    if [ "${CONDA_AUTO_ACTIVATE_BASE:-false}" != "true" ]; then
        log_warn "CONDA_AUTO_ACTIVATE_BASE 未设置为 true"
    else
        log_info "CONDA_AUTO_ACTIVATE_BASE 已正确设置"
    fi

    if [ -n "${CONDA_DEFAULT_ENV}" ] && [ "${CONDA_DEFAULT_ENV}" != "base" ]; then
        log_warn "CONDA_DEFAULT_ENV 设置为 ${CONDA_DEFAULT_ENV}，建议设为 base"
    fi
}

# 修复环境问题
fix_env() {
    log_info "开始修复环境问题..."

    # 1. 切换到 base 环境
    switch_to_base

    # 2. 清理环境历史
    clean_env_history

    # 3. 设置预防措施
    setup_prevention

    # 4. 验证修复效果
    log_info "验证修复效果..."
    monitor_env

    log_info "环境修复完成！"
    echo ""
    echo "建议操作:"
    echo "1. 重新启动终端以应用配置更改"
    echo "2. 定期运行: ./conda_fix.sh monitor"
    echo "3. 如需切换到其他环境，请使用: conda activate <env_name>"
}

# 显示帮助信息
show_help() {
    cat << EOF
Conda 环境修复和预防脚本

用法: $0 [command]

Commands:
    fix         修复当前环境问题
    monitor     监控环境状态
    prevent     设置预防措施
    clean       清理环境历史记录
    help        显示此帮助信息

示例:
    $0 fix           # 修复环境问题
    $0 monitor       # 查看环境状态
    $0 prevent       # 设置预防措施
    $0 clean         # 清理环境历史

EOF
}

# 主函数
main() {
    check_conda

    case "${1:-help}" in
        fix)
            fix_env
            ;;
        monitor)
            monitor_env
            ;;
        prevent)
            setup_prevention
            ;;
        clean)
            clean_env_history
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"