{"permissions": {"allow": ["<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(python test:*)", "Bash(rm:*)", "Bash(ls:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python:*)", "Bash(brew install:*)", "Bash(conda info:*)", "<PERSON><PERSON>(source:*)", "Bash(find:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(neo4j-admin:*)", "Bash(brew services start:*)", "Bash(brew services:*)", "Bash(cypher-shell:*)", "mcp__ide__executeCode", "mcp__openags-paper-search-mcp__search_arxiv", "mcp__openags-paper-search-mcp__search_google_scholar", "mcp__nickc<PERSON><PERSON>-duckduckgo-mcp-server__search", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(grep:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(conda create:*)"], "deny": [], "defaultMode": "acceptEdits"}}