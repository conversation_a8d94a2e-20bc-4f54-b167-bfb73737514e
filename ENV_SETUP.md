HopRAG 项目环境管理 — 新策略说明

新的环境管理策略采用了"默认 base 环境，按需激活特定环境"的原则。

## 环境管理策略

### 1. 默认行为
- **默认使用 base 环境**：不再自动激活特定 conda 环境
- **按需激活**：只有在项目需要特定环境时才手动激活
- **灵活切换**：提供便捷的环境切换工具

### 2. 环境管理工具

项目提供了三种环境管理脚本：

#### 便捷脚本 (推荐)
```bash
./env                    # 显示环境状态
./env activate          # 激活项目环境
./env install           # 安装项目环境
./env base              # 切换到 base 环境
./env update            # 更新项目环境
./env list              # 列出所有环境
```

#### Python 版本
```bash
python3 env_manager.py status     # 显示环境状态
python3 env_manager.py activate   # 激活项目环境
python3 env_manager.py install    # 安装项目环境
```

#### Shell 版本
```bash
./env-manager.sh status           # 显示环境状态
./env-manager.sh activate         # 激活项目环境
```

### 3. direnv 配置

`.envrc` 文件已更新为：
- 不自动激活任何环境
- 显示环境信息和状态
- 提供手动激活提示
- 保留环境验证功能

### 4. 使用流程

#### 初次使用
```bash
# 1. 查看环境状态
./env status

# 2. 安装项目环境（如果不存在）
./env install

# 3. 激活项目环境
./env activate
```

#### 日常使用
```bash
# 激活项目环境
./env activate

# 切换回 base 环境
./env base

# 查看环境状态
./env status
```

### 5. 项目环境配置

- **环境名称**：从 `.conda-env` 或 `environment.yml` 读取
- **环境定义**：`environment.yml` 文件
- **依赖管理**：通过 conda 环境管理

### 6. VS Code 集成

对于 VS Code 用户：
1. 使用集成终端
2. 运行 `./env activate` 激活项目环境
3. 或者使用命令面板手动选择解释器

### 7. 故障排除

如果遇到问题：
```bash
# 检查环境状态
./env status

# 重新安装环境
./env install

# 允许 direnv（如果需要）
direnv allow
```

## 优势

1. **更安全**：不影响其他项目和系统环境
2. **更灵活**：用户可控制环境激活时机
3. **更透明**：明确显示环境状态和操作指导
4. **跨平台**：支持多种脚本环境管理方式
