#!/usr/bin/env python3
"""
简化的离线节点构建测试脚本
只处理一个文档来验证流程是否正常
"""
import os
import json
import pickle
from HopBuilder import QABuilder
from config import node_name

def test_single_doc_build():
    print("开始测试单文档离线构建...")
    
    # 选择一个文档进行测试
    docs_dir = "quickstart_dataset/hotpot_example_docs"
    test_doc = "SpaceDev.txt"
    
    # 创建测试缓存目录
    cache_dir = 'quickstart_dataset/cache_test'
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
    
    # 初始化构建器
    builder = QABuilder(done=set(), label=node_name)
    
    # 读取单个文档
    doc_path = os.path.join(docs_dir, test_doc)
    print(f"处理文档: {doc_path}")
    
    with open(doc_path, 'r') as f:
        doc_content = f.read()
        print(f"文档长度: {len(doc_content)} 字符")
    
    try:
        # 生成问答对
        print("生成问答对...")
        sentence2node = builder.get_single_doc_qa(doc_content)
        print(f"生成了 {len(sentence2node)} 个节点")
        
        # 保存结果
        node_id = 1
        docid2nodes = {test_doc: []}
        node2questiondict = {}
        
        for text, tup in sentence2node.items():
            node = {'text': tup[0], 'keywords': sorted(list(tup[1])), 'embed': tup[2]}
            node2questiondict[(node_id, test_doc)] = (node, tup[3])
            docid2nodes[test_doc].append(node_id)
            node_id += 1
        
        # 保存到缓存
        with open(f'{cache_dir}/docid2nodes.json', 'w') as f:
            json.dump(docid2nodes, f)
        
        with open(f'{cache_dir}/node2questiondict.pkl', 'wb') as f:
            pickle.dump(node2questiondict, f)
        
        print(f"成功处理 {test_doc}")
        print(f"生成节点数: {len(docid2nodes[test_doc])}")
        print(f"缓存保存到: {cache_dir}")
        
        # 显示一些示例结果
        for i, (key, value) in enumerate(list(node2questiondict.items())[:2]):
            node_data, questions = value
            print(f"\n节点 {i+1}:")
            print(f"  文本: {node_data['text'][:100]}...")
            print(f"  关键词: {node_data['keywords'][:5]}")
            print(f"  问题类型: {list(questions.keys())}")
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_single_doc_build()
    if success:
        print("\n✅ 单文档离线构建测试成功!")
    else:
        print("\n❌ 单文档离线构建测试失败!")