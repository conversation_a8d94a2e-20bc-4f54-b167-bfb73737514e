import sys
import os
from openai import OpenAI
import json
import pickle
from config import *
from config import is_device_available
import torch
import re
import time
# from paddlenlp import Taskflow  # 暂时注释掉避免依赖问题
from sentence_transformers import SentenceTransformer
from modelscope import AutoModelForCausalLM, AutoTokenizer,AutoModelForSequenceClassification
import numpy as np
from typing import List, Tuple, Dict, Set, Optional
import logging

def sparse_similarity(a:Set, b:Set) -> float:
    # Handle empty sets to prevent division by zero
    if len(a) == 0 and len(b) == 0:
        return 1.0  # Both sets are empty, completely similar
    
    union = a.union(b)
    if len(union) == 0:
        return 1.0  # Edge case: both sets empty
    
    return len(a.intersection(b)) / len(union)

def try_run(func, *args, **kwargs):
    retry = 0
    while retry < max_try_num:
        try:
            return func(*args, **kwargs)
        except Exception as e:
            retry += 1
            with open(exception_log_path, "a") as file:
                file.write(f"Exception: {e}\n")
                file.write(f"Commandline: {sys.argv}\n")
            time.sleep(3)
    else:
        with open(exception_log_path, "a") as file:
            file.write(f"--FAIL--\n")
            file.write(f"Commandline: {sys.argv}\n")
        #exit(555)
        return None,None,None

def replace_newlines(match):
    # Replace \n and \r in the matched string
    return match.group(0).replace('\n', '\\n').replace('\r', '\\r')

def clean_json_str(json_str: str) -> str:
    """
    The generated JSON format may be non-standard, perform replacement processing first.
    :param json_str:
    :return:
    """
    # Remove code block markers ```
    # Replace None with null in the JSON string

    if "```json" not in json_str:
        index=json_str.index('{')
        json_str = json_str[index:]
        json_str = "```json"+json_str
    json_str = json_str.replace("None","null")
    if not json_str.startswith('```') and '```' in json_str:
        json_str = '```'+json_str.split('```')[1]
    json_str = json_str.split('}')[0]+'}'
    if json_str.startswith("```") and not json_str.endswith("```"):
        json_str += "```"
    match = re.search(r'```json(.*?)```', json_str, re.DOTALL)
    if match:
        json_str = match.group(1)
    match = re.search(r'```(.*?)```', json_str, re.DOTALL)
    if match:
        json_str = match.group(1)
    # Replace \n and \r in the matched string
    json_str = re.sub( r'("(?:\\.|[^"\\])*")', replace_newlines, json_str)
    # Remove trailing commas after key-value pairs
    json_str = re.sub(r',\s*}', '}', json_str)
    json_str = re.sub(r',\s*]', ']', json_str)
    # Restore the missing commas
    json_str = re.sub(r'\"\s+\"', '\",\"', json_str)
    # Inplacement of True and False
    json_str = json_str.replace("True","true")
    json_str = json_str.replace("False","false")
    return json_str

def txt2obj(text):  
    try: 
        text = clean_json_str(text) 
        text = text.replace("\\n", "\n").replace("\\t", "\t").replace('\\"', '"').replace("\\'", "'")
        text = text.replace('`','').replace('"','"').replace('"','"')
        return json.loads(text) 
    except Exception as e:
        # 增强容错机制：尝试从响应中提取关键信息
        if LOG:
            print(f"JSON解析失败: {e}")
            print(f"原始响应: {repr(text)}")
        
        # 尝试从文本中提取决策相关信息（用于推理查询）
        if any(keyword in text for keyword in ["Relevant and Necessary", "Indirectly Relevant", "Completely Irrelevant"]):
            if "Relevant and Necessary" in text:
                return {"Decision": "Relevant and Necessary"}
            elif "Indirectly Relevant" in text:
                return {"Decision": "Indirectly Relevant"}
            elif "Completely Irrelevant" in text:
                return {"Decision": "Completely Irrelevant"}
        
        # 尝试提取Question List（用于问题生成）
        question_pattern = r'\["([^"]+)"(?:,\s*"([^"]+)")*\]'
        match = re.search(question_pattern, text)
        if match:
            questions = re.findall(r'"([^"]+)"', match.group(0))
            return {"Question List": questions}
        
        return None




def get_title_keywords_eng(title_template, doc,query_generator)->Tuple[str,Set[str]]:
    chat = []
    chat.append({"role": "user", "content": title_template.format(doc_content=doc)})
    title, chat = get_chat_completion(chat, keys=["Title"],model=query_generator,max_tokens=4096)
    if len(title)==0:
        title=doc[:20]
    keywords=get_ner_eng(title)
    if len(keywords)==0:
        keywords=title.replace(',',"").replace('，',"").replace('。','').replace('.','')
        keywords=set(keywords)
        return title,keywords
    return title,set(keywords)

def get_title_keywords_zh(title_template, doc,query_generator)->Tuple[str,Set[str]]:
    chat = []
    chat.append({"role": "user", "content": title_template.format(doc_content=doc)})
    title, chat = get_chat_completion(chat, keys=["Title"],model=query_generator,max_tokens=4096)
    if len(title)==0:
        title=doc[:20]
    keywords=get_ner_zh(title)
    if len(keywords)==0:
        keywords=title.replace(',',"").replace('，',"").replace('。','').replace('.','')
        keywords=set(keywords.split())  # Split Chinese words by spaces
        return title,keywords
    return title,set(keywords)

def get_question_list(extract_template, sentences,query_generator)->List[str]:
    chat = []
    chat.append({"role": "user", "content": extract_template.format(sentences=sentences)})
    question_list, chat = get_chat_completion(chat, keys=["Question List"],model=query_generator,max_tokens=4096)
    return question_list


def get_ner_eng(text):
    """
    简化版关键词提取，避免PaddleNLP依赖
    使用基本的词汇过滤和清理
    """
    import string
    
    # 基本文本清理
    text = text.lower()
    # 移除标点符号
    text = text.translate(str.maketrans('', '', string.punctuation))
    
    # 分词
    words = text.split()
    
    # 过滤停用词（英文常见停用词）
    stop_words = {
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
        'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
        'before', 'after', 'above', 'below', 'between', 'among', 'within',
        'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
        'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might',
        'this', 'that', 'these', 'those', 'i', 'me', 'my', 'myself', 'we', 
        'our', 'ours', 'ourselves', 'you', 'your', 'yours', 'yourself', 
        'yourselves', 'he', 'him', 'his', 'himself', 'she', 'her', 'hers',
        'herself', 'it', 'its', 'itself', 'they', 'them', 'their', 'theirs',
        'themselves', 'what', 'which', 'who', 'whom', 'whose', 'where', 'when',
        'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most',
        'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same',
        'so', 'than', 'too', 'very', 's', 't', 'can', 'just', 'don', 'now'
    }
    
    # 过滤停用词和短词
    filtered = []
    for word in words:
        if (len(word) > 2 and  # 长度大于2
            word not in stop_words and  # 不是停用词
            word.isalpha()):  # 只包含字母
            filtered.append(word)
    
    # 去重并返回
    return list(set(filtered))

def get_ner_zh(text):
    """
    中文关键词提取，使用基本的词汇过滤和清理
    """
    import string
    import re
    
    # 基本文本清理
    text = re.sub(r'[^\w\s]', '', text)  # 移除标点符号但保留中文字符
    
    # 简单的中文分词（按空格和常见标点分割）
    words = re.findall(r'[\w]+', text)
    
    # 过滤中文停用词
    stop_words = {
        '的', '是', '在', '有', '和', '与', '或', '但', '不', '没有', '这', '那',
        '一个', '一些', '很', '非常', '比较', '更', '最', '也', '还', '就',
        '都', '只', '才', '已经', '正在', '将要', '可能', '应该', '必须',
        '因为', '所以', '如果', '虽然', '但是', '然而', '而且', '而', '或者',
        '我', '你', '他', '她', '它', '我们', '你们', '他们', '她们', '它们'
    }
    
    # 过滤停用词和短词
    filtered = []
    for word in words:
        if (len(word) > 1 and  # 长度大于1（中文词汇特点）
            word not in stop_words and  # 不是停用词
            not word.isdigit()):  # 不是纯数字
            filtered.append(word)
    
    # 去重并返回
    return list(set(filtered))

def load_embed_model(model_name):
    if model_name == "lm_studio":
        from lm_studio_integration import LMStudioEmbedding
        return LMStudioEmbedding()
    elif model_name in embed_model_dict:
        # Device verification and fallback logic
        device_to_use = llm_device
        if not is_device_available(llm_device):
            print(f"Device '{llm_device}' is not available, falling back to 'cpu'")
            device_to_use = 'cpu'
        else:
            print(f"Using device: {device_to_use}")
        
        return SentenceTransformer(embed_model_dict[model_name], device=device_to_use)  #
    else:
        raise NotImplementedError
    
def load_language_model(model_name):
    if 'gpt' in model_name or 'glm' in model_name:
        return model_name
    model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype="auto",
            device_map=llm_device) #
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    return model,tokenizer

def load_rerank_model(model_name):
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSequenceClassification.from_pretrained(model_name)
    model.eval()
    model.to(llm_device)
    return model,tokenizer

def get_doc_embeds(documents, model):
    with torch.no_grad():
        if isinstance(documents, str):
            documents = [documents]  # 确保输入是列表格式
        embeddings = model.encode(documents, normalize_embeddings=True, device=llm_device)
        # 如果是单个文档，返回单个向量；否则返回向量列表
        if len(documents) == 1:
            return embeddings[0]  # 返回numpy数组而不是列表
        return embeddings  # 返回numpy数组
    return embeddings

def _get_chat_completion(chat, return_json=True, model=default_gpt_model, max_tokens=4096, keys=None):
    if not isinstance(chat, list):
        chat = [{"role": "user", "content": chat}]
    if type(model)== str and ('gpt' in model or 'glm' in model):
        client = OpenAI(api_key=personal_key, base_url=personal_base)
        
        # 针对GLM模型优化API调用参数
        if 'glm' in model:
            # GLM模型优化参数：较低温度确保更一致的输出格式
            temperature = 0.05
            # 对于需要JSON格式的请求，不强制设置response_format以提高兼容性
            response_format_param = {} if return_json else {"response_format": {"type": "text"}}
        else:
            temperature = 0.1
            response_format_param = {"response_format": {"type": "json_object" if return_json else "text"}}
        
        chat_completion = client.chat.completions.create(
            model=model,
            messages=chat,
            max_tokens=max_tokens,
            temperature=temperature,
            frequency_penalty=0.0,
            presence_penalty=0.0,
            **response_format_param
        )
        
        # GLM特殊响应处理：优先使用reasoning_content，其次使用content
        message = chat_completion.choices[0].message
        response = message.content or getattr(message, 'reasoning_content', '') or ''
        
        print(response)
        chat = chat + [{"role": "assistant", "content": response}]
    elif type(model)==tuple:
        model,tokenizer=model
        text = tokenizer.apply_chat_template(
                    chat,
                    tokenize=False,
                    add_generation_prompt=True
                )
        model_inputs = tokenizer([text], return_tensors="pt").to(model.device)

        generated_ids = model.generate(
            **model_inputs,
            max_new_tokens=max_tokens
        )
        generated_ids = [
            output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
        ]

        response = tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
        print(response)
    else:
        raise NotImplementedError
    if not return_json:
        return response, chat
    obj = txt2obj(response)
    if obj is None:
        # 如果 JSON 解析失败，返回空值
        obj = tuple([None for _ in keys]) if keys else None
        return *obj, chat
    obj = tuple([obj[key] for key in keys if key in obj]) #
    return *obj, chat

def get_chat_completion(chat, return_json=True, model=default_gpt_model, max_tokens=4096, keys=None):
    return try_run(_get_chat_completion, chat, return_json, model, max_tokens, keys)

def pending_dot_answerable(pending_df,answerable_df):
    pending=np.array(pending_df['embedding'].tolist())
    answerable=np.array(answerable_df['embedding'].tolist())
    if torch.cuda.is_available() and llm_device != 'cpu':
        pending=torch.tensor(pending).to(llm_device)
        answerable=torch.tensor(answerable).to(llm_device)
        dense_similarity=pending.mm(answerable.T).cpu().numpy()
    else:
        dense_similarity=pending.dot(answerable.T)
    outcome=dense_similarity.flatten().tolist()
    del pending,answerable,dense_similarity
    if torch.cuda.is_available() and llm_device != 'cpu':
        torch.cuda.empty_cache()
    return outcome

def sparse_similarities_df(df)->Dict[Tuple[str,str],float]:
    if os.path.exists('/path/to/cache/sparse_similarities_result.pkl'):
        with open('/path/to/cache/sparse_similarities_result.pkl','rb') as file:
            return pickle.load(file)
    docs_keywords=df['keywords'].astype(str).unique()
    sparse_similarities={}
    for i in range(len(docs_keywords)):
        for j in range(i,len(docs_keywords)):  
            try:
                # Try to evaluate keywords as Python literal
                keywords_i = eval(docs_keywords[i]) if docs_keywords[i] != '[]' else []
                keywords_j = eval(docs_keywords[j]) if docs_keywords[j] != '[]' else []
                sparse_similarities[(docs_keywords[i],docs_keywords[j])]=sparse_similarity(set(keywords_i),set(keywords_j))
            except:
                # If evaluation fails or keywords are empty, set similarity to 0
                sparse_similarities[(docs_keywords[i],docs_keywords[j])]=0.0
            sparse_similarities[(docs_keywords[j],docs_keywords[i])]=sparse_similarities[(docs_keywords[i],docs_keywords[j])]
    return sparse_similarities



if __name__ == "__main__":
    print(get_chat_completion([{"role": "user", "content": "What is the capital of China?"}]))