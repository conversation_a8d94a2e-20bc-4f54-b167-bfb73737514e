
# Conda 环境守护脚本
# 此脚本确保始终使用正确的环境

import os
import sys

def check_environment():
    current_env = os.environ.get('CONDA_DEFAULT_ENV', 'base')

    # 如果当前环境不是 base 或期望的环境，切换到 base
    if current_env not in ['base', os.environ.get('EXPECTED_ENV', 'base')]:
        print(f"警告: 当前环境是 {current_env}，正在切换到 base")
        os.system("conda activate base")

    # 设置环境变量防止意外激活
    os.environ['CONDA_AUTO_ACTIVATE_BASE'] = 'true'
    os.environ['CONDA_DEFAULT_ENV'] = 'base'

if __name__ == "__main__":
    check_environment()
