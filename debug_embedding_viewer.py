#!/usr/bin/env python3
"""
HopRAG Embedding内容查看器
帮助用户查看embedding前后的文档内容和向量数据
"""

import os
import json
import pickle
import numpy as np
from tool import load_embed_model, get_doc_embeds
from config import embed_model

def show_current_embedding_model():
    """显示当前使用的embedding模型信息"""
    print("=== 当前Embedding模型信息 ===")
    print(f"配置的模型: {embed_model}")
    
    try:
        model = load_embed_model(embed_model)
        print(f"模型类型: {type(model)}")
        
        # 测试embedding
        test_text = "This is a test sentence for embedding."
        embedding = get_doc_embeds(test_text, model)
        print(f"输出维度: {embedding.shape}")
        print(f"向量范数: {np.linalg.norm(embedding):.4f}")
        print(f"前5个值: {embedding[:5]}")
        print("✅ 模型加载和embedding生成正常")
        
        return model
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None

def view_raw_documents(docs_dir="quickstart_dataset/hotpot_example_docs", limit=3):
    """查看原始文档内容"""
    print(f"\n=== 原始文档内容 (前{limit}个) ===")
    
    docs = os.listdir(docs_dir)[:limit]
    for doc_name in docs:
        doc_path = os.path.join(docs_dir, doc_name)
        try:
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📄 文档: {doc_name}")
            print(f"长度: {len(content)} 字符")
            print(f"内容预览: {content[:200]}...")
            
        except Exception as e:
            print(f"❌ 无法读取 {doc_name}: {e}")

def view_cached_nodes(cache_dir="quickstart_dataset/cache_hotpot_offline"):
    """查看缓存的节点数据"""
    print(f"\n=== 缓存节点数据 ===")
    
    # 检查缓存文件是否存在
    docid2nodes_path = os.path.join(cache_dir, "docid2nodes.json")
    node2questiondict_path = os.path.join(cache_dir, "node2questiondict.pkl")
    
    if not os.path.exists(docid2nodes_path):
        print(f"❌ 缓存文件不存在: {docid2nodes_path}")
        return
    
    if not os.path.exists(node2questiondict_path):
        print(f"❌ 缓存文件不存在: {node2questiondict_path}")
        return
    
    # 读取文档到节点映射
    with open(docid2nodes_path, 'r') as f:
        docid2nodes = json.load(f)
    
    print(f"处理的文档数量: {len(docid2nodes)}")
    print(f"文档列表: {list(docid2nodes.keys())[:5]}...")  # 显示前5个
    
    # 读取节点详细数据
    with open(node2questiondict_path, 'rb') as f:
        node2questiondict = pickle.load(f)
    
    print(f"节点总数: {len(node2questiondict)}")
    
    # 显示几个节点的详细信息
    print(f"\n=== 节点详细信息 (前3个) ===")
    for i, ((node_id, doc_id), data) in enumerate(list(node2questiondict.items())[:3]):
        node, questiondict = data
        
        print(f"\n🔹 节点 {i+1}:")
        print(f"  节点ID: {node_id}")
        print(f"  文档ID: {doc_id}")
        print(f"  文本内容: {node['text'][:150]}...")
        print(f"  关键词: {node['keywords'][:10]}...")  # 前10个关键词
        print(f"  Embedding形状: {node['embed'].shape if hasattr(node['embed'], 'shape') else 'N/A'}")
        print(f"  问题字典大小: {len(questiondict)}")

def test_embedding_process():
    """测试embedding处理过程"""
    print(f"\n=== Embedding处理测试 ===")
    
    model = load_embed_model(embed_model)
    
    # 测试文档
    test_docs = [
        "This is the first test document about machine learning.",
        "The second document discusses natural language processing.",
        "Finally, this document covers deep learning techniques."
    ]
    
    print("🔍 测试文档:")
    for i, doc in enumerate(test_docs, 1):
        print(f"  {i}. {doc}")
    
    print("\n🔄 生成Embeddings...")
    embeddings = []
    for i, doc in enumerate(test_docs, 1):
        emb = get_doc_embeds(doc, model)
        embeddings.append(emb)
        print(f"  文档{i} -> 向量形状: {emb.shape}, 范数: {np.linalg.norm(emb):.4f}")
    
    # 计算相似度
    print("\n📊 文档相似度矩阵:")
    embeddings = np.array(embeddings)
    similarity_matrix = np.dot(embeddings, embeddings.T)
    
    for i in range(len(test_docs)):
        for j in range(len(test_docs)):
            print(f"{similarity_matrix[i][j]:.4f}", end=" ")
        print()

def view_embedding_comparison():
    """对比不同模型的embedding结果"""
    print(f"\n=== Embedding模型对比 ===")
    
    test_text = "Machine learning is a subset of artificial intelligence."
    print(f"测试文本: {test_text}")
    
    models_to_test = ['lm_studio', 'bge_en'] if embed_model == 'lm_studio' else ['bge_en']
    
    for model_name in models_to_test:
        try:
            print(f"\n🔄 使用模型: {model_name}")
            model = load_embed_model(model_name)
            embedding = get_doc_embeds(test_text, model)
            
            print(f"  向量维度: {embedding.shape}")
            print(f"  向量范数: {np.linalg.norm(embedding):.4f}")
            print(f"  前5个值: {embedding[:5]}")
            print(f"  后5个值: {embedding[-5:]}")
            
        except Exception as e:
            print(f"  ❌ {model_name} 失败: {e}")

def main():
    """主函数"""
    print("🔍 HopRAG Embedding内容查看器")
    print("=" * 50)
    
    # 1. 显示当前模型信息
    model = show_current_embedding_model()
    if not model:
        return
    
    # 2. 查看原始文档
    view_raw_documents()
    
    # 3. 查看缓存数据（如果存在）
    view_cached_nodes()
    
    # 4. 测试embedding处理
    test_embedding_process()
    
    # 5. 模型对比（如果可用）
    view_embedding_comparison()
    
    print("\n✅ 内容查看完成！")

if __name__ == "__main__":
    main()